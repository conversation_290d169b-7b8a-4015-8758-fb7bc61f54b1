{"v": "5.9.6", "fr": 30, "ip": 0, "op": 57, "w": 1080, "h": 1080, "nm": "Mater", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "BULAT KUNING", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [292.5, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-216, -68, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [1, 1, 0.167], "y": [0.287, 0.287, 0]}, "t": 11, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 21, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [100, 100, 100]}, {"t": 40, "s": [0, 0, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 4;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [132, 132], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.800000059838, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-216, -68], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 11, "op": 57, "st": 11, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "BULAT TENGAH", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [540, 556, 0], "to": [0, -7.333, 0], "ti": [0, 7.333, 0]}, {"t": 7, "s": [540, 512, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "a": {"a": 0, "k": [-216, -68, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [0.200000017881, 0.200000017881, 0.200000017881, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [1, 0.800000071526, 0, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [1, 0.800000071526, 0, 1]}, {"t": 49, "s": [0.200000017881, 0.200000017881, 0.200000017881, 1]}], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [132, 132], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.176470588235, 0.717647058824, 0.356862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-216, -68], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 57, "st": -8, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "BULAT HITAM", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [791, 536, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-216, -68, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, -15.667]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 15, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [1, 1, 0.167], "y": [0.287, 0.287, 0]}, "t": 37, "s": [0, 0, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar n, n, t, t, v, amp, freq, decay;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < 1) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    amp = 0.05;\n    freq = 4;\n    decay = 8;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [132, 132], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.20000001496, 0.20000001496, 0.20000001496, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-216, -68], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 57, "st": -21, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 1, "nm": "LIQUID H ke K", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 1, "k": [{"t": 11, "s": [{"i": [[0.406, -0.082], [1.531, -0.556], [1.473, -1.29], [1.056, -1.645], [0.583, -1.378], [-0.908, -8.229], [-3.274, -6.219], [-1.403, -1.592], [-1.398, -1.754], [-0.097, -0.556], [-0.289, -0.371], [-1.796, -1.31], [-1.423, -0.973], [-0.225, -0.228], [-0.068, -0.065], [-0.539, -0.298], [-0.425, -0.408], [-8.673, -0.857], [-0.842, 18.94], [8.065, 9.282], [3.941, 3.456], [1.192, 0.956], [1.083, 0.76], [6.782, 3.06], [8.184, 1.919], [4.471, -0.136]], "o": [[-2.404, 0.486], [-1.531, 0.556], [-1.192, 1.044], [-1.056, 1.645], [-2.907, 6.865], [0.908, 8.229], [0.99, 1.879], [1.403, 1.592], [0.323, 0.406], [0.097, 0.556], [1.394, 1.789], [1.796, 1.309], [0.032, 0.022], [0.225, 0.228], [0.419, 0.401], [0.539, 0.298], [5.915, 5.667], [24.313, 2.402], [0.443, -9.972], [-3.352, -3.858], [-1.027, -0.9], [-1.09, -0.874], [-6.122, -4.295], [-7.147, -3.225], [-4.629, -1.085], [-1.063, 0.032]], "v": [[735, 525], [729.302, 526.397], [725, 529], [721.544, 533.25], [719, 538], [716.364, 561.485], [723, 584], [726.694, 589.094], [731, 594], [731.525, 595.525], [732, 597], [736.978, 601.612], [742, 605], [742.472, 605.467], [743, 606], [744.495, 606.995], [746, 608], [773, 618], [823, 591], [807, 560], [796, 548], [792, 546], [789, 543], [772, 534], [750, 526], [737, 524]], "c": true}], "h": 1}, {"t": 12, "s": [{"i": [[7.306, -0.348], [4.719, -0.325], [3.945, -0.608], [3.003, -1.105], [1.893, -1.813], [0.803, -0.858], [0.737, -0.946], [0.69, -1.114], [0.665, -1.364], [0.31, -8.175], [-2.173, -6.278], [-1.733, -3.022], [-1.878, -2.478], [-9.549, -4.58], [-11.141, 0.794], [-6.483, 2.866], [-2.707, 4.081], [-0.675, 0.937], [-0.56, 1.139], [-0.302, 8.893], [0.716, 6.327], [1.087, 4.682], [2.014, 3.629], [1.315, 1.46], [0.878, 0.607], [6.074, 0.327]], "o": [[-5.325, 0.254], [-4.719, 0.325], [-3.945, 0.608], [-3.003, 1.105], [-0.889, 0.852], [-0.803, 0.859], [-0.737, 0.946], [-0.69, 1.115], [-2.934, 6.024], [-0.31, 8.175], [1.189, 3.435], [1.733, 3.022], [5.663, 7.474], [9.549, 4.58], [6.362, -0.454], [6.482, -2.866], [0.739, -1.114], [0.675, -0.937], [2.51, -5.102], [0.302, -8.893], [-0.638, -5.642], [-1.087, -4.682], [-0.861, -1.553], [-1.315, -1.46], [-4.397, -3.041], [-6.074, -0.327]], "v": [[747, 545], [731.891, 545.814], [718.852, 547.16], [708.387, 549.676], [701, 554], [698.467, 556.545], [696.163, 559.232], [694.027, 562.303], [692, 566], [687.17, 588.309], [690, 611], [694.483, 620.717], [700, 629], [723.392, 647.701], [755, 654], [775.241, 648.721], [790, 638], [792.134, 635.018], [794, 632], [797.919, 609.418], [797, 585], [794.532, 569.49], [790, 557], [786.512, 552.291], [783, 549], [767.182, 544.459]], "c": true}], "h": 1}, {"t": 13, "s": [{"i": [[4.633, -0.338], [7.388, -2.129], [8.909, -1.861], [7.753, -1.534], [5.293, -3.049], [0.148, -0.432], [0.208, -0.157], [0.549, -0.101], [0.38, -0.277], [0.128, -0.41], [0.25, -0.188], [0.532, -0.066], [0.399, -0.383], [1.014, -1.617], [1.026, -1.65], [1.696, -6.81], [-0.966, -8.643], [-10.161, -10], [-13.837, -1.418], [-7.505, 3.019], [-4.638, 5.086], [-3.484, 7.509], [-2.775, 8.164], [-2.431, 10.864], [0.057, 6.14], [2.853, 2.766]], "o": [[-8.538, 0.623], [-7.388, 2.129], [-8.696, 1.817], [-7.753, 1.534], [-0.198, 0.114], [-0.148, 0.432], [-0.424, 0.318], [-0.549, 0.101], [-0.236, 0.172], [-0.128, 0.41], [-0.473, 0.354], [-0.532, 0.066], [-0.919, 0.881], [-1.014, 1.617], [-3.181, 5.116], [-1.696, 6.81], [1.519, 13.581], [10.161, 10], [11.341, 1.161], [7.505, -3.019], [5.29, -5.802], [3.484, -7.509], [3.402, -10.009], [1.148, -5.131], [-0.06, -6.445], [-4.237, -4.108]], "v": [[752, 567], [728.779, 571.572], [705, 578], [679.948, 582.576], [660, 589], [659.508, 589.968], [659, 591], [657.467, 591.531], [656, 592], [655.511, 592.988], [655, 594], [653.445, 594.478], [652, 595], [649.081, 598.923], [646, 604], [638.389, 621.855], [637, 645], [655.761, 681.623], [693, 700], [721.028, 696.685], [739, 684], [751.886, 663.772], [761, 640], [771, 606], [774, 587], [769, 570]], "c": true}], "h": 1}, {"t": 14, "s": [{"i": [[5.416, -0.545], [8.482, -2.179], [9.659, -1.234], [9.115, -0.444], [8.688, -2.738], [2.256, -1.396], [1.166, -0.892], [0.638, -0.626], [1.32, -1.623], [0.872, -1.336], [1.234, -3.039], [-11.006, -14.707], [-6.724, -3.031], [-0.436, -0.227], [-4.102, -1.234], [-11.609, 10.86], [-4.83, 5.376], [-3.084, 4.46], [-2.571, 3.544], [-2.329, 3.176], [-0.795, 1.763], [-0.238, 0.411], [-1.913, 3.268], [-2.943, 5.775], [-2.004, 6.648], [3.911, 6.787]], "o": [[-10.413, 1.048], [-8.482, 2.18], [-10.167, 1.299], [-10.067, 0.49], [-3.057, 0.964], [-1.548, 0.958], [-0.697, 0.533], [-1.24, 1.215], [-1.283, 1.578], [-1.996, 3.057], [-9.009, 22.189], [5.143, 6.872], [0.362, 0.163], [3.105, 1.616], [21.129, 6.357], [4.848, -4.536], [4.33, -4.819], [2.943, -4.256], [2.826, -3.895], [1.527, -2.082], [0.157, -0.349], [2.336, -4.041], [2.303, -3.932], [2.138, -4.196], [2.041, -6.772], [-4.593, -7.97]], "v": [[715, 605], [687.435, 610.361], [661, 616], [632, 618], [603, 621], [592, 626], [586, 632], [583, 633], [580, 638], [576, 642], [570, 653], [578, 716], [598, 733], [599, 735], [611, 739], [665, 728], [679, 714], [692, 699], [700, 689], [707, 678], [710, 673], [712, 672], [718, 660], [727, 646], [733, 629], [734, 610]], "c": true}], "h": 1}, {"t": 15, "s": [{"i": [[16.409, -2.019], [5.311, -2.743], [2.524, -2.024], [0.865, -0.822], [1.683, -2.197], [1.008, -1.052], [0.977, -4.395], [0.058, -0.377], [-5.483, -10.538], [-1.675, -2.356], [-14.005, -3.385], [-11.026, 4.55], [-12.022, 7.495], [-1.56, 0.995], [-0.454, 0.341], [-0.732, 0.492], [-1.433, 1.096], [-0.91, 0.676], [-2.391, 1.586], [-2.204, 1.904], [-2.635, 2.386], [-4.713, 5.706], [-2.115, 3.132], [12.236, 3.787], [8.677, -0.539], [20.052, 5.338]], "o": [[-7.493, 0.922], [-3.49, 1.802], [-1.363, 1.093], [-1.78, 1.691], [-1.717, 2.242], [-3.77, 3.935], [-0.289, 1.302], [-2.66, 17.261], [1.432, 2.753], [8.655, 12.175], [16.827, 4.067], [14.634, -6.039], [1.326, -0.827], [0.432, -0.276], [0.811, -0.608], [2.256, -1.518], [0.846, -0.647], [3.095, -2.298], [2.741, -1.818], [2.185, -1.888], [5.662, -5.128], [2.139, -2.589], [7.062, -10.458], [-7.297, -2.259], [-28.645, 1.779], [-15.984, -4.255]], "v": [[530, 633], [511, 638], [501, 646], [497, 648], [492, 654], [487, 662], [479, 679], [478, 683], [485, 724], [489, 732], [526, 757], [571, 750], [609, 730], [614, 728], [615, 726], [618, 725], [624, 720], [627, 719], [636, 711], [643, 706], [650, 699], [664, 684], [671, 676], [670, 648], [648, 647], [576, 642]], "c": true}], "h": 1}, {"t": 16, "s": [{"i": [[3.503, -0.537], [4.618, -2.191], [3.9, -3.219], [2.844, -3.145], [1.65, -2.964], [1.363, -4.297], [0.405, -4.964], [-2.131, -6.996], [-3.231, -4.784], [-1.967, -2.171], [-1.531, -0.942], [-0.601, -0.504], [-0.644, -0.397], [-0.301, -0.379], [-0.351, -0.209], [-6.052, -1.775], [-4.14, -0.319], [-31.251, 16.357], [1.161, 10.687], [13.057, 3.958], [18.95, 14.421], [0.515, 0.386], [0.789, 0.649], [1.074, 0.897], [3.81, 2.573], [5.616, 1.376]], "o": [[-6.629, 1.017], [-4.618, 2.191], [-2.974, 2.455], [-2.844, 3.145], [-2.144, 3.85], [-1.363, 4.297], [-0.753, 9.23], [2.131, 6.997], [1.602, 2.373], [1.967, 2.171], [0.952, 0.586], [0.601, 0.504], [0.445, 0.274], [0.301, 0.379], [2.65, 1.581], [6.052, 1.775], [45.056, 3.471], [6.803, -3.561], [-1.127, -10.375], [-29.297, -8.881], [-0.483, -0.367], [-0.897, -0.673], [-1.104, -0.908], [-3.88, -3.242], [-4.872, -3.29], [-6.818, -1.671]], "v": [[443, 613], [426.454, 617.848], [414, 626], [405.008, 634.618], [398, 644], [392.696, 656.164], [390, 670], [392.512, 694.334], [401, 712], [406.554, 719.074], [412, 724], [414.231, 725.642], [416, 727], [417.071, 728.048], [418, 729], [432.382, 734.447], [449, 738], [576, 714], [601, 688], [567, 670], [503, 637], [502, 635], [499, 634], [496, 631], [485, 622], [467, 613]], "c": true}], "h": 1}, {"t": 17, "s": [{"i": [[5.319, -0.386], [2.82, -0.439], [2.716, -0.954], [4.369, -2.978], [1.609, -2.615], [0.709, -0.842], [1.318, -14.365], [-6.224, -9.829], [-23.493, -5.88], [-14.896, -1.753], [-18.314, 1.347], [-1.246, 10.342], [4.726, 4.29], [1.099, 0.869], [1.164, 0.994], [2.014, 1.719], [6.223, 6.698], [2.066, 3.115], [2.376, 3.222], [1.054, 1.132], [0.673, 0.96], [0.697, 1.04], [3.161, 4.406], [3.235, 2.146], [3.192, 1.57], [0.664, 0.267]], "o": [[-4.004, 0.291], [-2.82, 0.439], [-5.289, 1.858], [-3.322, 2.265], [-0.657, 1.068], [-7.903, 9.385], [-1.558, 16.985], [11.138, 17.59], [13.442, 3.365], [12.925, 1.521], [13.374, -0.984], [0.805, -6.679], [-1.224, -1.111], [-1.498, -1.184], [-2.289, -1.954], [-7.097, -6.058], [-3.379, -3.637], [-2.247, -3.387], [-1.707, -2.314], [-0.753, -0.808], [-0.79, -1.127], [-3.653, -5.455], [-3.118, -4.346], [-3.515, -2.332], [-1.646, -0.81], [-7.538, -3.026]], "v": [[379, 570], [369.035, 571.002], [361, 573], [347, 581], [337, 589], [335, 591], [319, 627], [328, 667], [383, 698], [427, 706], [477, 708], [513, 692], [498, 670], [494, 668], [491, 664], [484, 659], [465, 640], [455, 629], [448, 620], [444, 615], [443, 612], [440, 609], [430, 594], [419, 582], [409, 575], [405, 573]], "c": true}], "h": 1}, {"t": 18, "s": [{"i": [[6.234, -0.837], [4.67, -1.856], [3.846, -2.707], [1.204, -0.817], [0.856, -0.798], [1.227, -1.739], [0.822, -0.858], [0.298, -0.539], [0.408, -0.425], [0.815, -0.85], [1.617, -6.885], [-13.757, -11.28], [-5.467, -2.687], [-8.643, -3.098], [-12.415, -3.224], [-13.254, 11.614], [-0.165, 0.465], [1.492, 3.298], [1.58, 2.79], [1.004, 1.048], [2.187, 3.872], [6.781, 15.736], [7.785, 6.728], [2.127, 1.395], [4.71, 1.547], [1.397, 0.381]], "o": [[-6.474, 0.869], [-4.67, 1.856], [-1.327, 0.933], [-1.204, 0.817], [-1.27, 1.185], [-1.227, 1.739], [-0.401, 0.419], [-0.298, 0.539], [-0.803, 0.838], [-4.539, 4.738], [-6.091, 25.934], [3.883, 3.184], [8.351, 4.105], [12.215, 4.378], [15.306, 3.975], [0.647, -0.567], [2.6, -7.322], [-1.241, -2.743], [-1.802, -3.182], [-3.55, -3.706], [-8.525, -15.093], [-5.477, -12.71], [-1.343, -1.161], [-4.072, -2.671], [-1.229, -0.404], [-7.332, -1.999]], "v": [[324, 525], [307.529, 529.121], [295, 536], [291.147, 538.601], [288, 541], [284.164, 545.745], [281, 550], [280.005, 551.495], [279, 553], [277, 556], [270, 573], [288, 633], [303, 641], [328, 652], [365, 663], [429, 667], [432, 664], [428, 645], [424, 637], [419, 628], [411, 615], [388, 567], [370, 537], [364, 533], [353, 528], [349, 526]], "c": true}], "h": 1}, {"t": 19, "s": [{"i": [[13.071, -1.839], [9.96, -13.124], [-2.488, -18.019], [-0.988, -3.083], [-0.753, -1.53], [-0.558, -0.421], [-0.476, -0.725], [-0.269, -0.928], [-0.457, -0.572], [-3.195, -2.171], [-2.982, -1.512], [-1.17, -0.726], [-1.07, -0.461], [-2.722, -0.759], [-2.61, -0.887], [-2.461, -1.08], [-2.396, -0.743], [-6.169, -1.756], [-7.605, 12.286], [3.529, 14.772], [4.357, 12.464], [2.083, 2.237], [0.687, 0.828], [1.106, 1.054], [1.645, 1.199], [1.017, 0.567]], "o": [[-17.648, 2.482], [-9.96, 13.125], [0.295, 2.138], [0.988, 3.083], [0.291, 0.592], [0.558, 0.421], [0.469, 0.715], [0.269, 0.928], [2.238, 2.805], [3.195, 2.171], [1.251, 0.634], [1.17, 0.726], [2.5, 1.076], [2.723, 0.759], [2.76, 0.938], [2.461, 1.08], [4.598, 1.425], [13.478, 3.837], [7.932, -12.813], [-3.369, -14.1], [-1.896, -5.423], [-0.707, -0.759], [-1.256, -1.514], [-1.847, -1.76], [-1.449, -1.056], [-9.741, -5.431]], "v": [[292, 491], [249.398, 515.847], [237, 564], [239.156, 572.456], [242, 580], [243.362, 581.401], [245, 583], [246.009, 585.607], [247, 588], [255.442, 595.47], [265, 601], [268.636, 603.13], [272, 605], [279.918, 607.642], [288, 610], [295.773, 613.146], [303, 616], [320, 620], [369, 620], [361, 565], [352, 524], [345, 511], [344, 508], [340, 504], [335, 501], [331, 497]], "c": true}], "h": 1}, {"t": 20, "s": [{"i": [[2.879, -0.405], [2.598, -0.748], [3.24, -1.533], [1.573, -0.736], [0.513, -0.492], [0.539, -0.298], [0.425, -0.408], [2.859, -2.895], [1.874, -2.841], [1.035, -2.458], [1.044, -3.416], [0.623, -4.625], [-0.745, -5.399], [-1.246, -2.838], [-0.702, -1.37], [-0.062, -0.677], [-0.406, -0.613], [-1.266, -1.225], [-0.372, -0.246], [-0.77, -0.566], [-22.653, 1.958], [-4.26, 11.097], [-0.881, 6.245], [9.482, 12.929], [4.581, 3.176], [7.643, 1.055]], "o": [[-3.37, 0.474], [-2.597, 0.748], [-1.192, 0.565], [-1.574, 0.736], [-0.419, 0.401], [-0.539, 0.298], [-2.69, 2.577], [-2.859, 2.895], [-1.815, 2.752], [-1.035, 2.458], [-1.254, 4.101], [-0.623, 4.625], [0.315, 2.279], [1.246, 2.838], [0.347, 0.678], [0.062, 0.677], [0.565, 0.854], [1.266, 1.225], [0.936, 0.621], [11.93, 8.774], [14.754, -1.275], [2.037, -5.305], [3.06, -21.685], [-3.646, -4.971], [-6.396, -4.434], [-6.587, -0.909]], "v": [[280, 478], [271.403, 479.706], [263, 483], [258.491, 485.054], [255, 487], [253.505, 487.995], [252, 489], [243.388, 497.302], [236, 506], [231.922, 513.502], [229, 522], [226.001, 535.027], [226, 550], [228.71, 558.181], [232, 565], [232.456, 567.048], [233, 569], [236.145, 572.456], [239, 575], [241, 577], [305, 587], [331, 569], [334, 552], [331, 495], [323, 487], [300, 477]], "c": true}], "h": 1}, {"t": 21, "s": [{"i": [[11.148, -1.568], [4.88, -2.359], [4.194, -3.538], [1.374, -1.167], [1.013, -1.072], [0.25, -0.704], [0.524, -0.681], [0.597, -0.286], [0.294, -0.441], [0.24, -0.907], [0.406, -0.424], [0.697, -1.202], [0.8, -7.814], [-2.666, -5.913], [-0.247, -0.369], [-0.23, -0.51], [-0.246, -0.295], [-13.191, 7.312], [-0.727, 0.697], [-0.85, 0.815], [-1.758, 1.556], [-2.657, 3.074], [-3.03, 3.976], [-2.182, 3.725], [1.857, 9.432], [4.981, 2.519]], "o": [[-7.287, 1.025], [-4.88, 2.359], [-1.492, 1.259], [-1.374, 1.167], [-0.474, 0.501], [-0.251, 0.704], [-0.318, 0.413], [-0.597, 0.286], [-0.632, 0.947], [-0.24, 0.907], [-0.828, 0.864], [-3.384, 5.84], [-1.125, 10.98], [0.146, 0.323], [0.307, 0.459], [0.124, 0.274], [12.111, 14.533], [2.275, -1.261], [0.838, -0.803], [1.789, -1.714], [3.831, -3.393], [3.625, -4.194], [3.034, -3.981], [3.523, -6.014], [-1.376, -6.986], [-9.275, -4.69]], "v": [[280, 478], [262.18, 483.116], [249, 492], [244.641, 495.639], [241, 499], [240.038, 500.865], [239, 503], [237.482, 503.979], [236, 505], [234.831, 507.893], [234, 510], [233, 512], [226, 533], [230, 562], [232, 563], [232, 565], [234, 566], [284, 562], [291, 558], [294, 556], [299, 552], [307, 544], [316, 533], [324, 522], [330, 498], [315, 482]], "c": true}], "h": 1}], "ix": 1}, "o": {"a": 1, "k": [{"t": 10, "s": [0], "h": 1}, {"t": 11, "s": [100], "h": 1}, {"t": 22, "s": [0], "h": 1}], "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13, "s": [0.200000017881, 0.200000017881, 0.200000017881, 1]}, {"t": 18, "s": [1, 0.800000071526, 0, 1]}], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "sw": 1080, "sh": 1080, "sc": "#2db75b", "ip": 0, "op": 57, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 1, "nm": "LIQUID K ke H", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [540, 540, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 1, "k": [{"t": 36, "s": [{"i": [[6.836, -0.962], [4.811, -2.099], [3.234, -3.619], [0.234, -5.26], [-2.761, -4.94], [-2.2, -2.804], [-2.429, -2.609], [-2.26, -1.981], [-0.691, -0.242], [-0.685, -0.549], [-0.478, -0.546], [-0.542, -0.38], [-2.839, -1.572], [-3.561, -1.549], [-7.976, -1.81], [-5.635, 3.616], [-2.064, 4.306], [-0.326, 4.583], [5.627, 9.656], [6.421, 4.304], [0.974, 0.687], [1.041, 0.542], [6.975, 1.056]], "o": [[-4.964, 0.698], [-4.811, 2.099], [-3.234, 3.619], [-0.241, 5.437], [2.76, 4.94], [2.025, 2.582], [2.429, 2.609], [0.552, 0.484], [0.691, 0.242], [0.545, 0.437], [0.478, 0.546], [3.084, 2.164], [2.839, 1.572], [5.462, 2.376], [7.976, 1.81], [3.417, -2.193], [2.064, -4.306], [0.93, -13.07], [-5.627, -9.657], [-1.063, -0.712], [-0.974, -0.687], [-5.264, -2.739], [-6.975, -1.056]], "v": [[290, 463], [274.982, 467.165], [262.558, 475.712], [257, 489], [261.669, 504.974], [270, 517], [276.824, 524.951], [284, 532], [285.901, 532.951], [288, 534], [289.503, 535.543], [291, 537], [299.643, 542.461], [309, 547], [330.371, 554.494], [352, 553], [360.318, 542.793], [364, 529], [355.513, 494.426], [336, 473], [332.984, 470.872], [330, 469], [311.179, 463.225]], "c": true}], "h": 1}, {"t": 37, "s": [{"i": [[32.107, -4.075], [2.701, -0.709], [3.497, -1.693], [1.204, -0.431], [1.139, -0.771], [0.295, -0.599], [0.404, -0.322], [0.721, -0.245], [0.404, -0.451], [0.199, -0.436], [0.072, -0.107], [0.65, -0.891], [0.623, -1.267], [0.339, -8.489], [-0.892, -7.885], [-1.111, -4.82], [-1.971, -3.552], [-1.303, -1.442], [-0.885, -0.61], [-12.62, 0.803], [-8.977, 6.135], [-2.607, 5.352], [4.453, 13.006]], "o": [[-3.7, 0.47], [-2.701, 0.709], [-1.047, 0.506], [-1.204, 0.431], [-0.414, 0.28], [-0.295, 0.599], [-0.712, 0.567], [-0.721, 0.245], [-0.132, 0.148], [-0.199, 0.436], [-0.776, 1.171], [-0.65, 0.891], [-2.536, 5.158], [-0.339, 8.489], [0.584, 5.167], [1.111, 4.82], [0.901, 1.624], [1.303, 1.442], [9.161, 6.307], [15.498, -0.986], [4.511, -3.083], [5.837, -11.984], [-8.686, -25.37]], "v": [[322, 427], [312.848, 428.582], [304, 432], [300.569, 433.302], [297, 435], [295.993, 436.468], [295, 438], [292.769, 439.087], [291, 440], [290.454, 441.03], [290, 442], [287.885, 444.928], [286, 448], [281.929, 469.455], [283, 495], [285.46, 510.211], [290, 523], [293.512, 527.761], [297, 531], [338, 535], [378, 527], [388, 514], [390, 469]], "c": true}], "h": 1}, {"t": 38, "s": [{"i": [[9.038, -1.327], [3.5, -1.003], [2.308, -1.447], [0.839, -0.93], [0.955, -0.846], [2.19, -3.06], [2.019, -3.908], [3.322, -9.584], [2.448, -9.408], [-0.14, -8.835], [-8.614, -2.303], [-3.718, 0.595], [-3.058, 0.707], [-4.4, 1.201], [-4.659, 1.101], [-12.149, 16.083], [-1.569, 2.758], [-0.776, 6.117], [4.568, 9.057], [1.185, 1.737], [0.647, 0.811], [0.948, 1.054], [11.16, 3.501]], "o": [[-3.693, 0.542], [-3.5, 1.003], [-0.69, 0.433], [-0.839, 0.93], [-3.222, 2.852], [-2.19, 3.06], [-4.264, 8.255], [-3.322, 9.584], [-2.175, 8.359], [0.14, 8.835], [4.068, 1.087], [3.718, -0.595], [4.74, -1.096], [4.401, -1.201], [27.621, -6.529], [2.206, -2.92], [3.509, -6.167], [2.116, -16.674], [-1.198, -2.375], [-0.566, -0.83], [-0.993, -1.244], [-7.827, -8.707], [-7.894, -2.476]], "v": [[370, 381], [358.961, 383.321], [350, 387], [347.699, 389.191], [345, 392], [337.097, 400.708], [331, 411], [319.639, 438.136], [311, 467], [306.408, 494.542], [318, 513], [329.758, 513.346], [340, 511], [353.561, 507.504], [367, 504], [430, 482], [435, 475], [443, 454], [436, 415], [433, 408], [430, 406], [428, 402], [399, 383]], "c": true}], "h": 1}, {"t": 39, "s": [{"i": [[1.092, -0.133], [4.157, -1.509], [3.061, -2.03], [4.513, -4.969], [0.955, -1.076], [2.12, -2.517], [0.905, -1.131], [11.47, -20.603], [-6.806, -10.252], [-7.194, 1.595], [-22.802, 1.036], [-10.52, 3.315], [-2.491, 1.542], [-2.729, 2.547], [-0.396, 0.327], [-1.815, 3.19], [-1.487, 4.255], [10.153, 13.567], [2.422, 2.339], [3.809, 1.717], [0.436, 0.227], [4.388, 1.32], [4.076, 0.325]], "o": [[-4.947, 0.602], [-4.416, 1.602], [-6.107, 4.051], [-0.813, 0.895], [-2.424, 2.731], [-0.93, 1.105], [-13.216, 16.514], [-5.44, 9.772], [5.602, 8.439], [21.491, -4.765], [12.909, -0.586], [2.736, -0.862], [2.512, -1.555], [0.427, -0.398], [3.68, -3.04], [2.512, -4.415], [7.589, -21.71], [-2.311, -3.088], [-4.289, -4.142], [-0.362, -0.163], [-2.781, -1.447], [-4.168, -1.254], [-4.053, -0.323]], "v": [[445, 339], [432, 341], [418, 350], [401, 365], [399, 369], [392, 376], [389, 380], [356, 430], [346, 471], [376, 473], [441, 462], [477, 459], [488, 454], [495, 447], [497, 446], [505, 436], [511, 423], [502, 364], [495, 356], [482, 347], [481, 345], [469, 341], [456, 338]], "c": true}], "h": 1}, {"t": 40, "s": [{"i": [[8.167, -1.096], [7.125, -2.821], [7.139, -3.595], [6.004, -3.653], [5.592, -4.17], [5.094, -4.32], [5.817, -6.137], [2.747, -3.407], [2.1, -3.3], [1.175, -3.219], [-1.01, -3.064], [-4.533, -0.727], [-5.773, 0.389], [-11.934, -1.3], [-8.01, -2.085], [-16.265, 0.937], [-5.049, 2.316], [-6.097, 7.527], [-2.156, 9.395], [7.031, 9.986], [5.803, 4.14], [3.318, 1.003], [0.516, 0.186]], "o": [[-8.361, 1.122], [-7.125, 2.821], [-6.392, 3.218], [-6.004, 3.653], [-4.807, 3.584], [-5.094, 4.32], [-2.913, 3.073], [-2.747, 3.407], [-1.31, 2.059], [-1.175, 3.219], [1.929, 5.855], [6.457, 1.035], [12.079, -0.814], [9.886, 1.077], [14.436, 3.757], [7.023, -0.404], [7.536, -3.457], [6.435, -7.944], [4.738, -20.652], [-4.495, -6.383], [-3.637, -2.594], [-0.374, -0.113], [-7.524, -2.717]], "v": [[534, 323], [511.083, 329.146], [490, 339], [471.401, 349.286], [454, 361], [439.258, 372.585], [423, 388], [414.39, 397.83], [407, 408], [402.76, 416.246], [402, 426], [416, 433], [435, 433], [476, 433], [503, 438], [547, 448], [567, 443], [588, 425], [601, 399], [591, 349], [575, 332], [564, 327], [562, 325]], "c": true}], "h": 1}, {"t": 41, "s": [{"i": [[-0.111, 0.009], [6.016, -0.775], [6.138, -1.367], [9.813, -3.661], [8.32, -5.035], [3.964, -3.834], [-0.508, -4.452], [-2.93, -1.929], [-1.883, -0.789], [-5.095, -1.539], [-18.52, -14.094], [-0.515, -0.386], [-0.788, -0.642], [-5.174, -3.494], [-5.706, -1.384], [-9.332, 7.612], [-1.436, 19.445], [5.287, 8.8], [2.122, 2.534], [0.54, 0.706], [2.687, 2.079], [1.377, 0.766], [28.749, -2.112]], "o": [[-6.796, 0.531], [-6.016, 0.775], [-10.426, 2.322], [-9.813, 3.661], [-3.653, 2.211], [-3.964, 3.834], [0.399, 3.494], [2.93, 1.929], [6.569, 2.755], [27.631, 8.345], [0.483, 0.367], [0.894, 0.67], [4.929, 4.014], [4.572, 3.087], [19.666, 4.769], [13.158, -10.733], [1.185, -16.042], [-2.141, -3.564], [-0.626, -0.748], [-1.912, -2.5], [-2.204, -1.706], [-15.452, -8.59], [-0.618, 0.045]], "v": [[593, 343], [574.006, 344.873], [556, 348], [525.421, 356.965], [498, 370], [485.38, 379.319], [479, 392], [484.887, 400.028], [493, 404], [513, 410], [577, 443], [578, 445], [581, 446], [595, 458], [613, 467], [664, 456], [690, 409], [681, 371], [674, 363], [673, 360], [666, 355], [660, 350], [594, 342]], "c": true}], "h": 1}, {"t": 42, "s": [{"i": [[-0.103, 0.008], [6.796, -3.342], [0.691, -0.691], [0.249, -1.999], [-4.06, -3.55], [-1.106, -1.03], [-0.8, -0.686], [-1.028, -0.957], [-0.757, -0.681], [-2.097, -2.053], [-4.671, -5.275], [-3.314, -4.768], [-14.071, -7.822], [-14.079, 6.097], [-1.873, 1.795], [-1.45, 1.333], [-3.095, 14.237], [3.375, 7.324], [2.604, 3.63], [19.032, 5.036], [5.696, 1.193], [13.338, 1.334], [13.891, -1.019]], "o": [[-8.473, 0.696], [-1.387, 0.682], [-1.536, 1.536], [-1.162, 9.341], [1.926, 1.685], [0.747, 0.696], [1.738, 1.49], [0.728, 0.678], [2.359, 2.12], [5.759, 5.637], [4.397, 4.966], [9.796, 14.095], [15.335, 8.525], [3.87, -1.676], [1.688, -1.618], [9.906, -9.103], [2.476, -11.391], [-2.256, -4.895], [-9.632, -13.427], [-5.492, -1.453], [-12.49, -2.615], [-13.969, -1.397], [-0.625, 0.046]], "v": [[603, 373], [576, 378], [571, 382], [567, 388], [584, 411], [589, 416], [592, 417], [596, 422], [599, 423], [606, 430], [622, 448], [634, 463], [669, 504], [721, 506], [734, 499], [739, 495], [760, 459], [757, 423], [749, 409], [702, 384], [685, 379], [646, 374], [604, 372]], "c": true}], "h": 1}, {"t": 43, "s": [{"i": [[10.761, -1.633], [2.812, -1.14], [0.91, -3.008], [-0.909, -3.311], [-1.282, -2.716], [-1.652, -2.592], [-1.009, -1.696], [-7.308, -16.407], [-2.237, -5.274], [-3.137, -4.684], [-2.935, -1.947], [-3.466, -1.608], [5.358, 47.897], [4.604, 5.643], [3.402, 1.534], [0.37, 0.247], [0.511, 0.23], [0.407, 0.239], [2.197, 1.093], [2.181, 1.012], [1.633, 0.634], [10.294, 3.185], [10.691, 2.445]], "o": [[-2.841, 0.432], [-2.812, 1.14], [-0.919, 3.039], [0.909, 3.311], [1.385, 2.937], [1.652, 2.592], [10.585, 17.793], [2.446, 5.492], [2.844, 6.707], [2.753, 4.111], [2.817, 1.868], [45.6, 21.153], [-0.927, -8.287], [-3.437, -4.212], [-0.324, -0.146], [-0.46, -0.307], [-0.346, -0.156], [-2.109, -1.24], [-3.288, -1.636], [-1.407, -0.653], [-10.399, -4.039], [-10.631, -3.289], [-11.087, -2.536]], "v": [[663, 409], [654.051, 411.068], [648, 417], [648.349, 426.742], [652, 436], [656.783, 444.431], [661, 451], [685, 498], [692, 515], [703, 534], [713, 545], [722, 550], [812, 484], [801, 457], [789, 446], [788, 444], [786, 444], [785, 442], [778, 439], [768, 434], [763, 433], [733, 422], [701, 413]], "c": true}], "h": 1}, {"t": 44, "s": [{"i": [[15.736, -4.514], [1.688, -1.168], [0.668, -1.586], [-0.925, -5.386], [-0.222, -0.892], [-0.904, -3.849], [-1.524, -6.137], [-2.066, -9.202], [-2.813, -5.447], [-0.55, -0.669], [-0.534, -0.813], [-1.974, -1.928], [-2.295, -1.537], [-2.267, -1.08], [-3.474, -1.034], [-9.651, 1.079], [-3.131, 29.063], [4.589, 9.328], [0.836, 1.275], [0.892, 1.118], [7.508, 3.769], [6.607, 2.246], [9.194, 2.547]], "o": [[-2.581, 0.74], [-1.688, 1.168], [-1.923, 4.565], [0.925, 5.386], [0.862, 3.469], [0.904, 3.849], [1.923, 7.746], [2.066, 9.202], [0.264, 0.513], [0.55, 0.669], [1.81, 2.75], [1.974, 1.928], [2.459, 1.647], [2.267, 1.08], [7.819, 2.326], [26.263, -2.938], [1.083, -10.052], [-0.495, -1.005], [-1.148, -1.75], [-4.102, -5.14], [-9.356, -4.697], [-7.522, -2.557], [-13.803, -3.823]], "v": [[720, 455], [713.566, 457.866], [710, 462], [709.392, 478.255], [712, 489], [714.504, 499.498], [718, 514], [723.832, 540.724], [731, 564], [732.298, 565.775], [734, 568], [739.635, 574.91], [746, 580], [752.739, 583.96], [761, 587], [787, 590], [843, 534], [838, 500], [835, 497], [833, 492], [814, 478], [789, 469], [764, 461]], "c": true}], "h": 1}, {"t": 45, "s": [{"i": [[9.69, -1.591], [2.141, -0.616], [1.455, -1.046], [0.453, -0.648], [0.37, -0.819], [0.451, -0.166], [0.124, -0.179], [1.05, -5.574], [0.666, -6.183], [0.484, -7.314], [-1.641, -5.214], [-0.417, -0.8], [-0.219, -0.446], [-0.772, -1.03], [-1.032, -1.093], [-1.931, -1.603], [-1.7, -0.916], [-7.614, -0.95], [-6.292, 1.63], [-8.986, 9.653], [-1.394, 14.111], [11.379, 6.896], [11.73, 1.307]], "o": [[-2.979, 0.49], [-2.141, 0.616], [-0.818, 0.589], [-0.453, 0.648], [-0.072, 0.158], [-0.451, 0.166], [-3.133, 4.521], [-1.05, 5.574], [-0.704, 6.528], [-0.484, 7.315], [0.111, 0.354], [0.417, 0.8], [0.88, 1.788], [0.772, 1.03], [1.577, 1.671], [1.931, 1.603], [5.254, 2.828], [7.614, 0.95], [12.66, -3.279], [8.986, -9.653], [2.134, -21.604], [-7.266, -4.404], [-9.767, -1.088]], "v": [[771, 494], [763.356, 495.583], [758, 498], [756.163, 499.827], [755, 502], [754.039, 502.484], [753, 503], [747.15, 518.253], [745, 536], [742.742, 557.486], [744, 577], [744.919, 578.932], [746, 581], [748.386, 585.021], [751, 588], [756.408, 593.067], [762, 597], [782.221, 602.843], [804, 602], [837.45, 582.124], [854, 546], [836, 501], [807, 494]], "c": true}], "h": 1}, {"t": 46, "s": [{"i": [[9.743, -1.662], [7.376, -4.085], [5.207, -4.247], [0.559, -0.33], [0.377, -0.364], [1.751, -2.428], [1.231, -1.581], [0.588, -0.287], [0.322, -0.433], [0.22, -0.489], [0.247, -0.37], [0.23, -0.511], [0.247, -0.368], [-4.685, -9.294], [-7.116, -2.622], [-10.761, 5.402], [-5.755, 9.063], [-0.685, 1.521], [-0.224, 0.441], [-0.392, 0.916], [-0.709, 3.16], [-0.117, 0.523], [6.593, 7.326]], "o": [[-8.288, 1.414], [-7.376, 4.085], [-0.386, 0.315], [-0.559, 0.33], [-1.764, 1.707], [-1.751, 2.428], [-0.327, 0.419], [-0.588, 0.287], [-0.314, 0.422], [-0.146, 0.324], [-0.307, 0.46], [-0.146, 0.323], [-7.119, 10.601], [3.367, 6.678], [15.217, 5.606], [10.599, -5.321], [1.263, -1.988], [0.164, -0.364], [0.635, -1.254], [1.214, -2.833], [0.082, -0.367], [3.424, -15.344], [-5.406, -6.007]], "v": [[822, 508], [798.188, 516.875], [779, 530], [777.493, 530.963], [776, 532], [770.6, 538.594], [766, 545], [764.497, 545.989], [763, 547], [763, 549], [761, 550], [761, 552], [759, 553], [752, 586], [770, 600], [818, 597], [844, 574], [846, 569], [848, 568], [849, 564], [851, 557], [853, 554], [846, 513]], "c": true}], "h": 1}], "ix": 1}, "o": {"a": 1, "k": [{"t": 35, "s": [0], "h": 1}, {"t": 36, "s": [100], "h": 1}], "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "ef": [{"ty": 21, "nm": "Fill", "np": 9, "mn": "ADBE Fill", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Fill Mask", "mn": "ADBE Fill-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "All Masks", "mn": "ADBE Fill-0007", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 2, "nm": "Color", "mn": "ADBE Fill-0002", "ix": 3, "v": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39, "s": [1, 0.800000071526, 0, 1]}, {"t": 44, "s": [0.200000017881, 0.200000017881, 0.200000017881, 1]}], "ix": 3}}, {"ty": 7, "nm": "Invert", "mn": "ADBE Fill-0006", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 0, "nm": "<PERSON>tal Feather", "mn": "ADBE Fill-0003", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 0, "nm": "Vertical Feather", "mn": "ADBE Fill-0004", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "Opacity", "mn": "ADBE Fill-0005", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}]}], "sw": 1080, "sh": 1080, "sc": "#2db75b", "ip": 25, "op": 57, "st": 25, "bm": 0}], "markers": []}