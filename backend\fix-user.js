require('dotenv').config()
const mongoose = require('mongoose')
const User = require('./models/User')
const Otp = require('./models/OTP')

const fixUser = async () => {
    try {
        // Connect to database
        await mongoose.connect(process.env.MONGO_URI)
        console.log('Connected to DB')

        // Find the user with the wrong email
        const user = await User.findOne({ email: "<EMAIL>" })
        
        if (user) {
            console.log('Found user:', user.name, user.email)
            
            // Option 1: Update email to a correct one
            const newEmail = "<EMAIL>" // Change this to your real email
            
            // Option 2: Just verify the user without changing email
            const updatedUser = await User.findByIdAndUpdate(
                user._id, 
                { 
                    isVerified: true,
                    // email: newEmail  // Uncomment this line and set correct email if you want to change it
                }, 
                { new: true }
            )
            
            // Delete any pending OTPs for this user
            await Otp.deleteMany({ user: user._id })
            
            console.log('User updated successfully:', updatedUser.email, 'Verified:', updatedUser.isVerified)
        } else {
            console.log('No user found with email: <EMAIL>')
            
            // List all users to see what's in the database
            const allUsers = await User.find({}, 'name email isVerified')
            console.log('All users in database:')
            allUsers.forEach(u => console.log(`- ${u.name} (${u.email}) - Verified: ${u.isVerified}`))
        }
        
        await mongoose.connection.close()
        console.log('Database connection closed')
        
    } catch (error) {
        console.error('Error:', error)
        process.exit(1)
    }
}

fixUser()
